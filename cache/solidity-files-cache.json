{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Library/CloudStorage/OneDrive-个人/Work/solidity/contracts/Faucet.sol": {"lastModificationDate": 1754043286629, "contentHash": "710cb2902fcfd4fc3a3e59bb1fc4a03c", "sourceName": "contracts/Faucet.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Faucet"]}}}