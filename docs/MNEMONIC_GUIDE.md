# Hardhat 助记词配置指南

## 概述

本项目使用助记词（Mnemonic）而非私钥来管理账户，这提供了更好的安全性和便利性。

## 助记词 vs 私钥

### 助记词的优势：
- **安全性**：一个助记词可以派生多个账户，降低密钥管理复杂度
- **便利性**：符合 BIP39 标准，与主流钱包兼容
- **可恢复性**：更容易记忆和备份

### 配置说明

#### 1. 环境变量配置

在 `.env` 文件中配置助记词：

```bash
# 测试网助记词
SEPOLIA_MNEMONIC="word1 word2 word3 ... word12"

# 主网助记词（生产环境）
MAINNET_MNEMONIC="word1 word2 word3 ... word12"
```

#### 2. HD 钱包路径

项目使用标准的 HD 钱包派生路径：`m/44'/60'/0'/0`

- `m/44'`：BIP44 标准
- `60'`：以太坊币种代码
- `0'`：账户索引
- `0`：外部链（接收地址）

#### 3. 账户派生

从助记词派生的账户索引：
- 账户 0：`m/44'/60'/0'/0/0`
- 账户 1：`m/44'/60'/0'/0/1`
- 账户 2：`m/44'/60'/0'/0/2`
- ...

## 使用示例

### 1. 获取派生账户

```javascript
const { ethers } = require("hardhat");

async function getAccounts() {
  const accounts = await ethers.getSigners();
  console.log("可用账户：");
  
  for (let i = 0; i < accounts.length; i++) {
    console.log(`账户 ${i}: ${accounts[i].address}`);
  }
}
```

### 2. 部署合约

```javascript
const { ethers } = require("hardhat");

async function deploy() {
  // 获取第一个账户作为部署者
  const [deployer] = await ethers.getSigners();
  
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));
  
  // 部署合约
  const Contract = await ethers.getContractFactory("YourContract");
  const contract = await Contract.deploy();
  
  console.log("合约地址:", await contract.getAddress());
}
```

## 安全最佳实践

### 1. 助记词安全
- ✅ 使用强随机性生成的助记词
- ✅ 安全存储助记词（离线备份）
- ✅ 定期轮换测试网助记词
- ❌ 不要在代码中硬编码助记词
- ❌ 不要将助记词提交到版本控制

### 2. 环境隔离
- **开发环境**：使用测试助记词
- **测试网**：使用专用测试助记词
- **主网**：使用硬件钱包或安全的助记词

### 3. 权限控制
- 测试网：可以使用多个派生账户
- 主网：建议只使用第一个账户，降低风险

## 故障排除

### 1. 助记词格式错误
```
Error: Invalid mnemonic
```
**解决方案**：确保助记词是 12 或 24 个有效的 BIP39 单词，用空格分隔。

### 2. 账户余额不足
```
Error: insufficient funds for gas
```
**解决方案**：确保派生账户有足够的 ETH 支付 gas 费用。

### 3. 网络连接问题
```
Error: could not detect network
```
**解决方案**：检查 RPC URL 是否正确，网络是否可访问。

## 工具推荐

### 1. 助记词生成
- [Ian Coleman's BIP39 Tool](https://iancoleman.io/bip39/)
- MetaMask 钱包
- Hardware wallets (Ledger, Trezor)

### 2. 账户查看
```bash
# 查看派生账户
npx hardhat console --network sepolia
```

```javascript
// 在控制台中执行
const accounts = await ethers.getSigners();
accounts.forEach((account, i) => {
  console.log(`账户 ${i}: ${account.address}`);
});
```

## 注意事项

1. **测试优先**：在主网部署前，务必在测试网充分测试
2. **备份重要**：助记词丢失将无法恢复账户
3. **版本兼容**：确保使用的 Hardhat 版本支持助记词配置
4. **Gas 管理**：监控各个派生账户的 gas 使用情况
