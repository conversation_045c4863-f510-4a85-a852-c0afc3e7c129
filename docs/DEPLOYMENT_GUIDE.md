# Faucet 合约部署指南

## 概述

本指南详细说明如何使用提供的部署脚本在不同网络上部署 Faucet 合约。

## 部署脚本说明

### 1. `scripts/deploy.js` - 完整版部署脚本

**功能特点：**
- 完整的部署流程和详细日志
- 自动为合约提供初始资金（0.1 ETH）
- 全面的错误处理和状态验证
- 支持所有网络（本地、测试网、主网）

**适用场景：**
- 本地开发和测试
- 有充足余额的测试网部署
- 生产环境部署

### 2. `scripts/deploy-testnet.js` - 测试网简化版

**功能特点：**
- 简化的部署流程
- 不提供初始资金，降低 gas 要求
- 专门针对测试网优化

**适用场景：**
- 测试网部署（余额较少）
- 快速部署验证

## 部署前准备

### 1. 环境检查

```bash
# 检查 Node.js 版本
node --version  # 建议 >= 16.0.0

# 检查依赖安装
npm list hardhat ethers dotenv

# 编译合约
npm run compile
```

### 2. 账户准备

```bash
# 验证账户配置
npm run verify:accounts

# 检查特定网络的账户
npx hardhat run scripts/verify-accounts.js --network sepolia
```

### 3. 网络配置验证

确保 `.env` 文件配置正确：
```bash
SEPOLIA_RPC_URL="https://your-rpc-url"
SEPOLIA_MNEMONIC="your mnemonic phrase"
```

## 部署操作

### 本地网络部署

```bash
# 方法 1: 使用 npm 脚本
npm run deploy:local

# 方法 2: 直接使用 hardhat
npx hardhat run scripts/deploy.js --network hardhat

# 方法 3: 启动本地节点后部署
npx hardhat node  # 在另一个终端
npx hardhat run scripts/deploy.js --network localhost
```

**预期结果：**
- 部署者账户：10000 ETH
- 合约初始资金：0.1 ETH
- Gas 费用：约 0.0002 ETH

### Sepolia 测试网部署

#### 选项 1: 完整版部署（推荐余额 > 0.2 ETH）

```bash
# 使用完整版脚本
npm run deploy:sepolia

# 或直接使用 hardhat
npx hardhat run scripts/deploy.js --network sepolia
```

#### 选项 2: 简化版部署（最低余额 0.001 ETH）

```bash
# 使用简化版脚本
npm run deploy:testnet

# 或直接使用 hardhat
npx hardhat run scripts/deploy-testnet.js --network sepolia
```

### 获取测试 ETH

如果账户余额不足，可以从以下水龙头获取测试 ETH：

**Sepolia 测试网水龙头：**
- https://sepoliafaucet.com/
- https://faucet.sepolia.dev/
- https://sepolia-faucet.pk910.de/

## 部署后验证

### 1. 合约状态检查

```bash
# 进入 Hardhat 控制台
npx hardhat console --network sepolia

# 在控制台中执行
const faucet = await ethers.getContractAt("Faucet", "YOUR_CONTRACT_ADDRESS");
console.log("合约余额:", ethers.formatEther(await ethers.provider.getBalance(faucet.address)));
```

### 2. 功能测试

```javascript
// 获取合约实例
const faucet = await ethers.getContractAt("Faucet", "CONTRACT_ADDRESS");

// 测试 drip 功能（需要合约有余额）
await faucet.drip();

// 测试 withdraw 功能
await faucet.withdraw(ethers.parseEther("0.01"));

// 为合约充值
const [deployer] = await ethers.getSigners();
await deployer.sendTransaction({
  to: "CONTRACT_ADDRESS",
  value: ethers.parseEther("0.1")
});
```

### 3. 区块浏览器验证

**Sepolia 网络：**
- 合约地址：https://sepolia.etherscan.io/address/YOUR_CONTRACT_ADDRESS
- 交易记录：https://sepolia.etherscan.io/tx/YOUR_TX_HASH

## 常见问题解决

### 1. 余额不足错误

```
Error: insufficient funds
```

**解决方案：**
- 检查账户余额：`npx hardhat run scripts/verify-accounts.js --network sepolia`
- 从水龙头获取测试 ETH
- 使用简化版部署脚本：`npm run deploy:testnet`

### 2. 网络连接错误

```
Error: could not detect network
```

**解决方案：**
- 检查 `.env` 文件中的 RPC URL
- 验证网络连接
- 尝试使用其他 RPC 提供商

### 3. Gas 费用过高

```
Error: gas required exceeds allowance
```

**解决方案：**
- 等待网络拥堵缓解
- 调整 gas 价格设置
- 使用简化版部署脚本

### 4. 助记词错误

```
Error: Invalid mnemonic
```

**解决方案：**
- 检查助记词格式（12 或 24 个单词）
- 确保单词之间用空格分隔
- 验证助记词的有效性

## 部署最佳实践

### 1. 安全考虑

- ✅ 使用环境变量存储敏感信息
- ✅ 定期轮换测试网助记词
- ✅ 生产环境使用硬件钱包
- ❌ 不要在代码中硬编码私钥或助记词

### 2. 测试流程

1. **本地测试**：先在本地网络测试部署
2. **测试网验证**：在测试网验证功能
3. **代码审计**：生产环境前进行代码审计
4. **分阶段部署**：逐步部署到生产环境

### 3. 监控和维护

- 监控合约余额
- 定期检查合约功能
- 备份重要的部署信息
- 保持部署脚本的更新

## 脚本命令总结

```bash
# 编译合约
npm run compile

# 验证账户配置
npm run verify:accounts

# 本地网络部署
npm run deploy:local

# Sepolia 完整部署
npm run deploy:sepolia

# Sepolia 简化部署
npm run deploy:testnet

# 清理编译缓存
npm run clean
```

## 支持和帮助

如果遇到问题，请：

1. 检查本指南的常见问题部分
2. 查看部署脚本的详细错误信息
3. 验证网络配置和账户状态
4. 参考 Hardhat 官方文档

---

**注意：** 本指南基于 Hardhat 2.26.1 和 ethers.js 6.x 版本编写。不同版本可能存在 API 差异。
