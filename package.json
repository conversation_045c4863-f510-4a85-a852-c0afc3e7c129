{"name": "solidity", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "clean": "hardhat clean", "deploy:local": "hardhat run scripts/deploy.js --network hardhat", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "deploy:testnet": "hardhat run scripts/deploy-testnet.js --network sepolia", "verify:accounts": "hardhat run scripts/verify-accounts.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.1.0", "hardhat": "^2.26.1"}, "dependencies": {"dotenv": "^17.2.1"}}