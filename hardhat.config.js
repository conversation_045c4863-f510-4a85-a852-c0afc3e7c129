// 导入 Hardhat 工具箱，包含编译、测试、部署等常用插件
require("@nomicfoundation/hardhat-toolbox");
// 加载环境变量配置
require("dotenv").config();

// 从环境变量中获取网络配置信息
const {
  SEPOLIA_RPC_URL,
  SEPOLIA_MNEMONIC,
} = process.env;

// 默认助记词（仅用于本地开发，请勿在生产环境使用）
const DEFAULT_MNEMONIC = "test test test test test test test test test test test junk";

module.exports = {
  // Solidity 编译器配置
  solidity: {
    version: "0.8.20", // 使用 Solidity 0.8.20 版本
    settings: {
      optimizer: { enabled: true, runs: 200 } // 启用优化器，优化 200 次运行
    }
  },
  // 网络配置
  networks: {
    // 本地 Hardhat 网络（默认）
    hardhat: {
      chainId: 31337,
      accounts: {
        mnemonic: DEFAULT_MNEMONIC,
        count: 10, // 生成 10 个账户
        accountsBalance: "10000000000000000000000" // 每个账户 10000 ETH
      }
    },

    // Sepolia 测试网配置
    sepolia: {
      url: SEPOLIA_RPC_URL,
      accounts: {
        mnemonic: SEPOLIA_MNEMONIC,
        path: "m/44'/60'/0'/0", // 标准 HD 钱包路径
        initialIndex: 0,       // 起始账户索引
        count: 10             // 派生账户数量
      },
      chainId: ********,
      gasPrice: "auto"
    },
  },
  // 项目路径配置
  paths: {
    sources: "contracts",   // 合约源码目录
    tests: "test",         // 测试文件目录
    cache: "cache",        // 编译缓存目录
    artifacts: "artifacts" // 编译产物目录
  }
};
