// 导入 Hardhat 工具箱，包含编译、测试、部署等常用插件
require("@nomicfoundation/hardhat-toolbox");
// 加载环境变量配置
require("dotenv").config();

// 从环境变量中获取网络配置信息
const { SEPOLIA_RPC_URL, SEPOLIA_PRIVATE_KEY, GOERLI_RPC_URL, GOERLI_PRIVATE_KEY } = process.env;

module.exports = {
  // Solidity 编译器配置
  solidity: {
    version: "0.8.20", // 使用 Solidity 0.8.20 版本
    settings: {
      optimizer: { enabled: true, runs: 200 } // 启用优化器，优化 200 次运行
    }
  },
  // 网络配置
  networks: {
    // Sepolia 测试网配置
    sepolia: {
      url: SEPOLIA_RPC_URL || "https://sepolia.example", // RPC 节点地址
      accounts: SEPOLIA_PRIVATE_KEY ? [SEPOLIA_PRIVATE_KEY] : [] // 部署账户私钥
    }
  },
  // 项目路径配置
  paths: {
    sources: "contracts",   // 合约源码目录
    tests: "test",         // 测试文件目录
    cache: "cache",        // 编译缓存目录
    artifacts: "artifacts" // 编译产物目录
  }
};
