# CRUSH.md

Repo status: Hardhat project configured with Solidity 0.8.20. Keep commands here for reliability.

Build/lint/test
- Solidity/Hardhat:
  - Install deps: npm i
  - Compile: npm run compile
  - Clean: npm run clean
  - Test: npm run test
  - Single test: npx hardhat test path/file.ts -g "pattern"

Code style
- Imports: absolute where configured, else relative; group std/lib -> third-party -> local; no unused imports
- Formatting: use project formatter (prettier/black/gofmt/forge fmt); 100-120 col limit; no trailing console/logs
- Types: prefer explicit types; avoid any; narrow unions; null-safe handling; immutable/const where possible
- Naming: camelCase for vars/funcs, PascalCase for types/classes, UPPER_SNAKE for consts/env; descriptive not cute
- Errors: fail fast with context; no silent catches; return typed errors/results; include revert reasons in Solidity
- Solidity: SPDX header; pragma pinned; use latest safe solidity version; checks-effects-interactions; custom errors; reentrancy guards; events for state changes; SafeCast/SafeTransfer libs
- Testing: fast, isolated, deterministic; arrange-act-assert; cover edge cases and reverts; use fixtures/forks sparingly
- Git: small commits; meaningful messages; no secrets; add .crush to .gitignore

Assistant notes
- Prefer running lint and typecheck before tests; cache deps; keep this file updated as real scripts are added.
