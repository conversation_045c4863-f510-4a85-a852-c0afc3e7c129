/**
 * 验证助记词配置和账户派生
 * 运行命令：npx hardhat run scripts/verify-accounts.js --network sepolia
 */

const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 验证助记词配置和账户派生...\n");

  try {
    // 获取所有签名者账户
    const signers = await ethers.getSigners();
    
    if (signers.length === 0) {
      console.log("❌ 未找到任何账户，请检查助记词配置");
      return;
    }

    console.log(`✅ 成功派生 ${signers.length} 个账户\n`);

    // 显示每个账户的信息
    for (let i = 0; i < Math.min(signers.length, 5); i++) {
      const signer = signers[i];
      const address = signer.address;
      
      try {
        const balance = await signer.provider.getBalance(address);
        const balanceInEth = ethers.formatEther(balance);
        
        console.log(`账户 ${i}:`);
        console.log(`  地址: ${address}`);
        console.log(`  余额: ${balanceInEth} ETH`);
        console.log(`  HD路径: m/44'/60'/0'/0/${i}`);
        console.log("");
      } catch (error) {
        console.log(`账户 ${i}:`);
        console.log(`  地址: ${address}`);
        console.log(`  余额: 无法获取 (${error.message})`);
        console.log("");
      }
    }

    if (signers.length > 5) {
      console.log(`... 还有 ${signers.length - 5} 个账户未显示\n`);
    }

    // 网络信息
    const network = await ethers.provider.getNetwork();
    console.log("🌐 网络信息:");
    console.log(`  网络名称: ${network.name}`);
    console.log(`  链 ID: ${network.chainId}`);
    console.log("");

    // 验证助记词派生的一致性
    console.log("🔐 助记词派生验证:");
    
    // 检查第一个账户是否符合预期
    const firstAccount = signers[0];
    console.log(`  主账户地址: ${firstAccount.address}`);
    
    // 如果是本地网络，显示更多信息
    if (network.chainId === 31337n) {
      console.log("  ℹ️  本地 Hardhat 网络，使用默认测试助记词");
    } else {
      console.log("  ℹ️  外部网络，使用环境变量中的助记词");
    }

    console.log("\n✅ 助记词配置验证完成！");

  } catch (error) {
    console.error("❌ 验证过程中出现错误:");
    console.error(error.message);
    
    if (error.message.includes("Invalid mnemonic")) {
      console.log("\n💡 解决建议:");
      console.log("1. 检查 .env 文件中的助记词格式");
      console.log("2. 确保助记词是 12 或 24 个有效单词");
      console.log("3. 单词之间用空格分隔");
    }
    
    if (error.message.includes("could not detect network")) {
      console.log("\n💡 解决建议:");
      console.log("1. 检查网络 RPC URL 是否正确");
      console.log("2. 确认网络连接正常");
      console.log("3. 验证 API 密钥是否有效");
    }
  }
}

// 错误处理
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("脚本执行失败:", error);
    process.exit(1);
  });
