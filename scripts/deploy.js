/**
 * Faucet 合约部署脚本
 * 
 * 功能说明：
 * - 使用 ethers.js 库部署 Faucet 合约
 * - 支持多网络部署（hardhat、sepolia）
 * - 提供详细的部署信息和状态反馈
 * - 包含完整的错误处理和日志输出
 * 
 * 使用方法：
 * - 本地网络：npx hardhat run scripts/deploy.js --network hardhat
 * - Sepolia 测试网：npx hardhat run scripts/deploy.js --network sepolia
 */

const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 开始部署 Faucet 合约...\n");

  try {
    // 1. 获取部署者账户信息
    console.log("📋 获取部署者账户信息...");
    const [deployer] = await ethers.getSigners();
    
    if (!deployer) {
      throw new Error("未找到部署者账户，请检查网络配置");
    }

    const deployerAddress = deployer.address;
    console.log(`✅ 部署者账户地址: ${deployerAddress}`);

    // 2. 获取部署者账户余额
    console.log("💰 检查部署者账户余额...");
    const balance = await deployer.provider.getBalance(deployerAddress);
    const balanceInEth = ethers.formatEther(balance);
    console.log(`✅ 部署者账户余额: ${balanceInEth} ETH`);

    // 3. 获取当前网络信息
    console.log("🌐 获取网络信息...");
    const network = await ethers.provider.getNetwork();
    console.log(`✅ 网络名称: ${network.name}`);
    console.log(`✅ 链 ID: ${network.chainId}`);
    console.log(`✅ RPC 提供商: ${deployer.provider.connection?.url || '本地节点'}\n`);

    // 4. 检查余额是否足够部署
    const minimumBalance = ethers.parseEther("0.01"); // 最少需要 0.01 ETH
    if (balance < minimumBalance) {
      console.log("⚠️  警告: 账户余额可能不足以支付 gas 费用");
      if (network.chainId !== 31337n) { // 非本地网络
        console.log("💡 建议: 请确保账户有足够的 ETH 支付 gas 费用");
      }
    }

    // 5. 获取合约工厂
    console.log("🏭 获取 Faucet 合约工厂...");
    const FaucetFactory = await ethers.getContractFactory("Faucet");
    console.log("✅ 合约工厂获取成功");

    // 6. 估算部署 gas 费用
    console.log("⛽ 估算部署 gas 费用...");
    try {
      const deploymentData = FaucetFactory.interface.encodeDeploy([]);
      const gasEstimate = await deployer.estimateGas({
        data: FaucetFactory.bytecode + deploymentData.slice(2)
      });
      console.log(`✅ 预估 gas 用量: ${gasEstimate.toString()}`);
      
      const gasPrice = await deployer.provider.getFeeData();
      if (gasPrice.gasPrice) {
        const estimatedCost = gasEstimate * gasPrice.gasPrice;
        console.log(`✅ 预估部署成本: ${ethers.formatEther(estimatedCost)} ETH`);
      }
    } catch (error) {
      console.log("⚠️  无法估算 gas 费用，继续部署...");
    }

    // 7. 部署合约
    console.log("\n🔨 开始部署合约...");
    const deployStartTime = Date.now();
    
    // Faucet 合约的构造函数是 payable 的，可以在部署时发送 ETH
    const initialFunding = ethers.parseEther("0.1"); // 初始资金 0.1 ETH
    let deploymentOptions = {};
    
    // 只有在账户余额足够时才提供初始资金
    if (balance > initialFunding + minimumBalance) {
      deploymentOptions.value = initialFunding;
      console.log(`💰 为合约提供初始资金: ${ethers.formatEther(initialFunding)} ETH`);
    } else {
      console.log("💰 跳过初始资金（余额不足）");
    }

    const faucetContract = await FaucetFactory.deploy(deploymentOptions);
    console.log("⏳ 等待部署交易确认...");

    // 8. 等待部署交易确认
    const deploymentReceipt = await faucetContract.waitForDeployment();
    const deployEndTime = Date.now();
    const deploymentTime = (deployEndTime - deployStartTime) / 1000;

    // 9. 获取合约地址和交易信息
    const contractAddress = await faucetContract.getAddress();
    const deploymentTx = faucetContract.deploymentTransaction();
    
    if (!deploymentTx) {
      throw new Error("无法获取部署交易信息");
    }

    // 10. 显示部署成功信息
    console.log("\n🎉 合约部署成功！");
    console.log("=" .repeat(50));
    console.log(`📍 合约地址: ${contractAddress}`);
    console.log(`🔗 部署交易哈希: ${deploymentTx.hash}`);
    console.log(`⏱️  部署耗时: ${deploymentTime.toFixed(2)} 秒`);
    console.log(`🧱 部署区块: ${deploymentTx.blockNumber || '待确认'}`);
    
    // 11. 获取交易详情
    try {
      const txReceipt = await deploymentTx.wait();
      if (txReceipt) {
        console.log(`⛽ 实际 gas 使用: ${txReceipt.gasUsed.toString()}`);
        console.log(`💸 实际 gas 费用: ${ethers.formatEther(txReceipt.gasUsed * txReceipt.gasPrice)} ETH`);
        console.log(`✅ 交易状态: ${txReceipt.status === 1 ? '成功' : '失败'}`);
      }
    } catch (error) {
      console.log("⚠️  无法获取详细交易信息");
    }

    // 12. 验证合约状态
    console.log("\n🔍 验证合约状态...");
    try {
      const contractBalance = await ethers.provider.getBalance(contractAddress);
      console.log(`💰 合约余额: ${ethers.formatEther(contractBalance)} ETH`);
      
      // 验证合约代码是否正确部署
      const contractCode = await ethers.provider.getCode(contractAddress);
      if (contractCode === "0x") {
        throw new Error("合约代码未正确部署");
      }
      console.log("✅ 合约代码验证成功");
      
    } catch (error) {
      console.log(`⚠️  合约状态验证失败: ${error.message}`);
    }

    // 13. 提供后续操作建议
    console.log("\n📚 后续操作建议:");
    console.log("1. 验证合约功能:");
    console.log(`   npx hardhat console --network ${network.name}`);
    console.log(`   const faucet = await ethers.getContractAt("Faucet", "${contractAddress}");`);
    console.log("2. 为合约充值:");
    console.log(`   await deployer.sendTransaction({to: "${contractAddress}", value: ethers.parseEther("1.0")});`);
    console.log("3. 测试水龙头功能:");
    console.log(`   await faucet.drip(); // 领取 0.01 ETH`);
    
    // 14. 网络特定建议
    if (network.chainId === 31337n) {
      console.log("\n🏠 本地网络部署完成");
      console.log("💡 提示: 本地网络重启后合约地址会改变");
    } else {
      console.log(`\n🌍 ${network.name} 网络部署完成`);
      console.log("💡 提示: 请保存合约地址用于后续交互");
      console.log(`🔗 区块浏览器: https://${network.name}.etherscan.io/address/${contractAddress}`);
    }

    console.log("\n✅ 部署脚本执行完成！");
    
    return {
      contractAddress,
      deploymentTx: deploymentTx.hash,
      deployer: deployerAddress,
      network: network.name,
      chainId: network.chainId
    };

  } catch (error) {
    console.error("\n❌ 部署过程中发生错误:");
    console.error(`错误类型: ${error.name || 'Unknown'}`);
    console.error(`错误信息: ${error.message}`);
    
    // 提供针对性的错误解决建议
    if (error.message.includes("insufficient funds")) {
      console.log("\n💡 解决建议:");
      console.log("1. 检查部署者账户是否有足够的 ETH");
      console.log("2. 降低 gas 价格或 gas 限制");
      console.log("3. 使用有余额的其他账户");
    }
    
    if (error.message.includes("nonce")) {
      console.log("\n💡 解决建议:");
      console.log("1. 等待之前的交易确认");
      console.log("2. 重置账户 nonce");
    }
    
    if (error.message.includes("network")) {
      console.log("\n💡 解决建议:");
      console.log("1. 检查网络连接");
      console.log("2. 验证 RPC URL 配置");
      console.log("3. 确认网络配置正确");
    }
    
    throw error; // 重新抛出错误以确保脚本以错误状态退出
  }
}

// 脚本入口点，包含错误处理
if (require.main === module) {
  main()
    .then((result) => {
      if (result) {
        console.log(`\n🎯 部署结果摘要:`);
        console.log(`   合约地址: ${result.contractAddress}`);
        console.log(`   网络: ${result.network} (${result.chainId})`);
        console.log(`   部署者: ${result.deployer}`);
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 脚本执行失败:", error.message);
      process.exit(1);
    });
}

// 导出主函数以供其他脚本使用
module.exports = { main };
