/**
 * Hardhat 控制台辅助脚本
 * 提供常用的合约交互命令和示例
 * 
 * 使用方法：
 * 1. 进入控制台：npx hardhat console --network sepolia
 * 2. 加载此脚本：.load scripts/console-helper.js
 * 3. 使用提供的辅助函数
 */

const { ethers } = require("hardhat");

// 辅助函数：获取合约实例
async function getFaucet(contractAddress) {
  try {
    const faucet = await ethers.getContractAt("Faucet", contractAddress);
    console.log(`✅ 成功连接到合约: ${contractAddress}`);
    return faucet;
  } catch (error) {
    console.error(`❌ 连接合约失败: ${error.message}`);
    return null;
  }
}

// 辅助函数：检查合约余额
async function checkFaucetBalance(faucet) {
  try {
    const contractAddress = await faucet.getAddress();
    const balance = await ethers.provider.getBalance(contractAddress);
    const balanceInEth = ethers.formatEther(balance);
    console.log(`💰 合约余额: ${balanceInEth} ETH`);
    return balanceInEth;
  } catch (error) {
    console.error(`❌ 获取余额失败: ${error.message}`);
    return null;
  }
}

// 辅助函数：检查账户余额
async function checkAccountBalance(address) {
  try {
    const balance = await ethers.provider.getBalance(address);
    const balanceInEth = ethers.formatEther(balance);
    console.log(`💰 账户 ${address} 余额: ${balanceInEth} ETH`);
    return balanceInEth;
  } catch (error) {
    console.error(`❌ 获取账户余额失败: ${error.message}`);
    return null;
  }
}

// 辅助函数：为合约充值
async function fundFaucet(faucet, amountInEth) {
  try {
    const [signer] = await ethers.getSigners();
    const contractAddress = await faucet.getAddress();
    
    console.log(`💸 正在向合约充值 ${amountInEth} ETH...`);
    const tx = await signer.sendTransaction({
      to: contractAddress,
      value: ethers.parseEther(amountInEth.toString())
    });
    
    console.log(`⏳ 等待交易确认: ${tx.hash}`);
    await tx.wait();
    
    console.log(`✅ 充值成功！`);
    await checkFaucetBalance(faucet);
    return tx;
  } catch (error) {
    console.error(`❌ 充值失败: ${error.message}`);
    return null;
  }
}

// 辅助函数：测试 drip 功能
async function testDrip(faucet) {
  try {
    const [signer] = await ethers.getSigners();
    const userAddress = signer.address;
    
    console.log(`🚰 测试 drip 功能...`);
    console.log(`👤 用户地址: ${userAddress}`);
    
    // 检查用户余额（之前）
    const balanceBefore = await ethers.provider.getBalance(userAddress);
    console.log(`💰 领取前余额: ${ethers.formatEther(balanceBefore)} ETH`);
    
    // 执行 drip
    const tx = await faucet.drip();
    console.log(`⏳ 等待交易确认: ${tx.hash}`);
    const receipt = await tx.wait();
    
    // 检查用户余额（之后）
    const balanceAfter = await ethers.provider.getBalance(userAddress);
    console.log(`💰 领取后余额: ${ethers.formatEther(balanceAfter)} ETH`);
    
    const received = balanceAfter - balanceBefore + receipt.gasUsed * receipt.gasPrice;
    console.log(`🎉 实际领取: ${ethers.formatEther(received)} ETH`);
    
    return tx;
  } catch (error) {
    console.error(`❌ drip 测试失败: ${error.message}`);
    return null;
  }
}

// 辅助函数：测试 withdraw 功能
async function testWithdraw(faucet, amountInEth) {
  try {
    const [signer] = await ethers.getSigners();
    const userAddress = signer.address;
    
    console.log(`💧 测试 withdraw 功能 (${amountInEth} ETH)...`);
    console.log(`👤 用户地址: ${userAddress}`);
    
    // 检查用户余额（之前）
    const balanceBefore = await ethers.provider.getBalance(userAddress);
    console.log(`💰 提取前余额: ${ethers.formatEther(balanceBefore)} ETH`);
    
    // 执行 withdraw
    const amount = ethers.parseEther(amountInEth.toString());
    const tx = await faucet.withdraw(amount);
    console.log(`⏳ 等待交易确认: ${tx.hash}`);
    const receipt = await tx.wait();
    
    // 检查用户余额（之后）
    const balanceAfter = await ethers.provider.getBalance(userAddress);
    console.log(`💰 提取后余额: ${ethers.formatEther(balanceAfter)} ETH`);
    
    const received = balanceAfter - balanceBefore + receipt.gasUsed * receipt.gasPrice;
    console.log(`🎉 实际提取: ${ethers.formatEther(received)} ETH`);
    
    return tx;
  } catch (error) {
    console.error(`❌ withdraw 测试失败: ${error.message}`);
    return null;
  }
}

// 辅助函数：显示网络信息
async function showNetworkInfo() {
  try {
    const network = await ethers.provider.getNetwork();
    const [signer] = await ethers.getSigners();
    
    console.log(`🌐 网络信息:`);
    console.log(`   名称: ${network.name}`);
    console.log(`   链 ID: ${network.chainId}`);
    console.log(`   当前账户: ${signer.address}`);
    
    await checkAccountBalance(signer.address);
  } catch (error) {
    console.error(`❌ 获取网络信息失败: ${error.message}`);
  }
}

// 辅助函数：显示使用帮助
function showHelp() {
  console.log(`
📚 Faucet 合约交互帮助

🔧 基础函数:
  getFaucet(address)           - 连接到合约
  checkFaucetBalance(faucet)   - 检查合约余额
  checkAccountBalance(address) - 检查账户余额
  showNetworkInfo()            - 显示网络信息

💰 资金操作:
  fundFaucet(faucet, amount)   - 为合约充值 (ETH)
  testDrip(faucet)            - 测试领取 0.01 ETH
  testWithdraw(faucet, amount) - 测试提取指定金额

📝 使用示例:
  // 1. 连接合约
  const faucet = await getFaucet("******************************************");
  
  // 2. 检查状态
  await showNetworkInfo();
  await checkFaucetBalance(faucet);
  
  // 3. 为合约充值
  await fundFaucet(faucet, 0.1);
  
  // 4. 测试功能
  await testDrip(faucet);
  await testWithdraw(faucet, 0.01);

💡 提示: 使用 showHelp() 随时查看此帮助信息
  `);
}

// 导出所有函数
module.exports = {
  getFaucet,
  checkFaucetBalance,
  checkAccountBalance,
  fundFaucet,
  testDrip,
  testWithdraw,
  showNetworkInfo,
  showHelp
};

// 如果在控制台中直接加载，显示欢迎信息
if (typeof global !== 'undefined' && global.console) {
  console.log(`
🎉 Faucet 控制台辅助工具已加载！

快速开始:
1. showHelp()                    - 查看完整帮助
2. await showNetworkInfo()       - 查看网络信息
3. const faucet = await getFaucet("YOUR_CONTRACT_ADDRESS")

输入 showHelp() 查看所有可用函数。
  `);
}
