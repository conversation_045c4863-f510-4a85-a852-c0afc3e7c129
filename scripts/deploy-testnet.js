/**
 * Faucet 合约测试网部署脚本（简化版）
 * 
 * 功能说明：
 * - 专门用于测试网部署，不提供初始资金
 * - 降低 gas 费用要求
 * - 适用于余额较少的测试账户
 * 
 * 使用方法：
 * - Sepolia 测试网：npx hardhat run scripts/deploy-testnet.js --network sepolia
 */

const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 开始部署 Faucet 合约（测试网版本）...\n");

  try {
    // 1. 获取部署者账户信息
    const [deployer] = await ethers.getSigners();
    const deployerAddress = deployer.address;
    console.log(`✅ 部署者账户地址: ${deployerAddress}`);

    // 2. 获取账户余额和网络信息
    const balance = await deployer.provider.getBalance(deployerAddress);
    const balanceInEth = ethers.formatEther(balance);
    const network = await ethers.provider.getNetwork();
    
    console.log(`💰 部署者账户余额: ${balanceInEth} ETH`);
    console.log(`🌐 网络: ${network.name} (链 ID: ${network.chainId})\n`);

    // 3. 检查最低余额要求
    const minimumBalance = ethers.parseEther("0.001"); // 最少需要 0.001 ETH
    if (balance < minimumBalance) {
      throw new Error(`余额不足！当前余额: ${balanceInEth} ETH，最少需要: 0.001 ETH`);
    }

    // 4. 获取合约工厂并部署（不提供初始资金）
    console.log("🔨 开始部署合约（无初始资金）...");
    const FaucetFactory = await ethers.getContractFactory("Faucet");
    const faucetContract = await FaucetFactory.deploy();
    
    console.log("⏳ 等待部署交易确认...");
    await faucetContract.waitForDeployment();

    // 5. 获取部署结果
    const contractAddress = await faucetContract.getAddress();
    const deploymentTx = faucetContract.deploymentTransaction();
    
    console.log("\n🎉 合约部署成功！");
    console.log("=" .repeat(40));
    console.log(`📍 合约地址: ${contractAddress}`);
    console.log(`🔗 交易哈希: ${deploymentTx?.hash}`);
    console.log(`🌍 网络: ${network.name}`);
    
    // 6. 验证合约
    const contractBalance = await ethers.provider.getBalance(contractAddress);
    console.log(`💰 合约余额: ${ethers.formatEther(contractBalance)} ETH`);
    
    console.log("\n📚 后续操作:");
    console.log("1. 为合约充值:");
    console.log(`   await deployer.sendTransaction({to: "${contractAddress}", value: ethers.parseEther("0.1")});`);
    console.log("2. 测试水龙头:");
    console.log(`   const faucet = await ethers.getContractAt("Faucet", "${contractAddress}");`);
    console.log(`   await faucet.drip();`);
    
    if (network.name === "sepolia") {
      console.log(`\n🔗 Etherscan: https://sepolia.etherscan.io/address/${contractAddress}`);
    }

    return { contractAddress, network: network.name };

  } catch (error) {
    console.error("\n❌ 部署失败:", error.message);
    
    if (error.message.includes("insufficient funds")) {
      console.log("\n💡 获取测试 ETH:");
      console.log("- Sepolia: https://sepoliafaucet.com/");
      console.log("- Sepolia: https://faucet.sepolia.dev/");
    }
    
    throw error;
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("脚本执行失败:", error.message);
      process.exit(1);
    });
}

module.exports = { main };
