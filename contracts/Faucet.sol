// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

// 简单水龙头合约：允许用户从合约中领取少量 ETH，并允许外部向合约充值
contract Faucet {
    // 事件：记录每次领取或发放的地址与金额，便于前端/索引服务订阅
    event FaucetDripped(address indexed to, uint256 amount);

    // 添加可接收 ETH 的构造函数
    constructor() payable {}

    // 可自定义领取金额的提款函数
    // _amount: 领取的 wei 数量，这里限制单次最多 0.1 ETH
    function withdraw(uint256 _amount) public {
        require(_amount <= 0.1 ether, "Too much"); // 限制单次领取上限
        require(address(this).balance >= _amount, "Insufficient faucet balance"); // 合约余额检查
        payable(msg.sender).transfer(_amount); // 转账给调用者
        emit FaucetDripped(msg.sender, _amount); // 记录事件日志
    }

    // 最简单的一键水龙头：固定发 0.01 ETH
    function drip() external {
        uint256 amount = 0.01 ether;
        require(address(this).balance >= amount, "Insufficient faucet balance");
        payable(msg.sender).transfer(amount);
        emit FaucetDripped(msg.sender, amount);
    }

    // 接收 ETH 的回退函数：允许直接向合约地址转账进行充值
    receive() external payable {}
}
